from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include
from django.views.generic import TemplateView
import debug_toolbar
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView
from apps.products.views_admin import delete_product_image, delete_product_variant_attribute_value

admin.site.site_header = 'Picky PC'
admin.site.index_title = 'Picky Administration'

urlpatterns = [
    path('admin/', admin.site.urls),
    # path('admin/', include('ordered_model.admin.urls')),
    # Custom API endpoints
    path('api/admin/delete-product-image/<int:image_id>/', delete_product_image, name='delete_product_image'),
    path('api/admin/delete-product-variant-attribute-value/<int:attribute_value_id>/',
         delete_product_variant_attribute_value, name='delete_product_variant_attribute_value'),
    # path('auth/social/', include('djoser.urls')),  # Put social auth under auth as a sub-route
    # path('auth/social/', include('djoser.urls.jwt')),
    # path('auth/social/', include('djoser.social.urls')),
    path('auth/', include('apps.core.urls')),
    path('auth/social/', include('social_django.urls', namespace='social')),  # Add this line
    path('api/products/', include('apps.products.urls')),
    path('api/cart/', include('apps.cart.urls')),
    path('api/customers/', include('apps.customers.urls')),
    path('api/orders/', include('apps.order.urls')),
    path('api/payments/', include('apps.payments.urls')),
    path('api/wishlist/', include('apps.wishlist.urls')),
    path('api/schema/doc/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/schema/re-doc/', SpectacularRedocView.as_view(url_name='schema'), name='re-doc'),
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api-auth/', include('rest_framework.urls')),
    path('', TemplateView.as_view(template_name='home.html')),
]

if settings.DEBUG:
    urlpatterns += [path('__debug__/', include(debug_toolbar.urls))]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
