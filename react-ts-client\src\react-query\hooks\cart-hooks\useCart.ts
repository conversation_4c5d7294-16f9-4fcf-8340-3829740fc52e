import { useQuery } from '@tanstack/react-query'
import APIClient from "../../services/api-client"
import cartStore from '../../../zustand/cartStore'
import { CACHE_KEY_CART_ITEMS } from '../constants'
import { CartShape } from '../../../types/types'


const useCart = () => {
  const { cartId } = cartStore()

  const apiClient = new APIClient<CartShape>(`/cart/${cartId}`)

  return useQuery({
    queryKey: [CACHE_KEY_CART_ITEMS, cartId],
    queryFn: apiClient.get,
    // enabled: !!cartId, // Only run the query if cartId is truthy
    // keepPreviousData: true,
    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
    // initialData:  Here we can add categories as static data
    // refetchOnMount: true,
    staleTime: 0,
  })
}

export default useCart
