import axios, { AxiosError, AxiosRequestConfig } from "axios"
import { FetchPaginatedResponse } from "../../types/types"

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_BACKEND_URL,
  withCredentials: true, // Include credentials (cookies) with requests
})

class APIClient<TResponse, TRequest = TResponse> {
  endpoint: string

  constructor(endpoint: string) {
    this.endpoint = endpoint
  }

  get = async (config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await axiosInstance.get<TResponse>(this.endpoint, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      // Add a return statement to ensure a return value in case of an error
      throw new Error((error as AxiosError).message)
    }
  }

  // This is for paginated responses from the server
  getAll = async (config?: AxiosRequestConfig): Promise<FetchPaginatedResponse<TResponse>> => {
    try {
      const response = await axiosInstance.get<FetchPaginatedResponse<TResponse>>(this.endpoint, config)
      console.log(`${response.config.url}`)
      return response.data
    } catch (error) {
      this.handleError(error)
      // Add a return statement to ensure a return value in case of an error
      throw new Error((error as AxiosError).message)
    }
  }

  post = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await axiosInstance.post<TResponse>(this.endpoint, data, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      // Add a return statement to ensure a return value in case of an error
      throw new Error((error as AxiosError).message)
    }
  }

  patch = async (data: Partial<TRequest>, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await axiosInstance.patch<TResponse>(this.endpoint, data, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw new Error((error as AxiosError).message)
    }
  }

  delete = async (itemId?: number): Promise<TResponse> => {
    try {
      const response = await axiosInstance.delete(`${this.endpoint}/${itemId}/`)
      return response.data
    } catch (error) {
      this.handleError(error)
      // Add a return statement to ensure a return value in case of an error
      throw new Error((error as AxiosError).message)
    }
  }

  private handleError = (error: unknown): void => {
    if (axios.isAxiosError(error)) {
      console.error("Error message: ", error.message)
      if (error.response) {
        console.error("Response data: ", error.response.data)
        console.error("Response status: ", error.response.status)
        // console.error("Response headers: ", error.response.headers)
        throw {
          message: error.message,
          response: error.response,
        }
      } else if (error.request) {
        console.error("Request data: ", error.request)
      } else {
        console.error("Error config: ", error.config)
      }
    } else {
      console.error("Error: ", error)
    }
    throw error // Re-throw the error to be handled by the calling function if needed
  }
}

export default APIClient
